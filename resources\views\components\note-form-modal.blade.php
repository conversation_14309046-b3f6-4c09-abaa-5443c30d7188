<!-- Modal de saisie de note modernisé - Version Livewire Pure -->
@if($noteActionMode === 'create' || $noteActionMode === 'edit')
<div class="livewire-modal-overlay" wire:click="quickCancel">
    <div class="livewire-modal-content modal-lg" wire:click.stop>
        <div class="modal-header {{ $noteActionMode === 'edit' ? 'bg-warning text-dark' : 'bg-primary text-white' }}">
            <h5 class="modal-title">
                <i class="fa {{ $noteActionMode === 'edit' ? 'fa-edit' : 'fa-plus-circle' }} me-2"></i>
                {{ $noteActionMode === 'edit' ? 'Modifier la note' : 'Ajouter une note' }}
            </h5>
            <button type="button" class="btn-close {{ $noteActionMode === 'edit' ? '' : 'btn-close-white' }}" wire:click="quickCancel" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <!-- Informations actuelles de la note (mode édition) -->
            @if($noteActionMode === 'edit' && $selectedNoteId)
                @php
                    $currentNote = \App\Models\Note::with(['matiere', 'typeNote'])->find($selectedNoteId);
                @endphp
                @if($currentNote)
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="current-note-score
                                @if($currentNote->valeur >= 16) excellent
                                @elseif($currentNote->valeur >= 14) good
                                @elseif($currentNote->valeur >= 10) average
                                @else poor
                                @endif">
                                {{ number_format($currentNote->valeur, 2) }}
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1">
                                <i class="fa fa-info-circle me-1"></i>
                                Note actuelle
                            </h6>
                            <div class="row g-2 text-sm">
                                <div class="col-md-6">
                                    <strong>Matière:</strong> {{ $currentNote->matiere->nom ?? 'Non défini' }}<br>
                                    <strong>Type:</strong> {{ $currentNote->typeNote->nom ?? 'Non défini' }}
                                </div>
                                <div class="col-md-6">
                                    <strong>Date:</strong> {{ $currentNote->created_at->format('d/m/Y') }}<br>
                                    <strong>Dernière modif:</strong> {{ $currentNote->updated_at->format('d/m/Y') }}
                                </div>
                            </div>
                            @if($currentNote->observation)
                            <div class="mt-2">
                                <strong>Observation actuelle:</strong>
                                <div class="bg-light p-2 rounded mt-1">
                                    <em>{{ $currentNote->observation }}</em>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fa fa-lightbulb me-1"></i>
                            Modifiez les champs ci-dessous pour mettre à jour cette note
                        </small>
                    </div>
                </div>
                @endif
            @endif
            
                <div class="row g-3">
                    <!-- Sélection du parcours -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-graduation-cap me-1 text-primary"></i>
                            Parcours <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('noteParcourId') is-invalid @enderror"
                                wire:model="noteParcourId"
                                wire:change="updateParcour($event.target.value)">
                            <option value="">-- Sélectionner un parcours --</option>
                            @foreach($parcours ?? [] as $parc)
                                <option value="{{ $parc->id }}">{{ $parc->sigle }} - {{ $parc->nom }}</option>
                            @endforeach
                        </select>
                        @error('noteParcourId') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <!-- Sélection de la matière -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-book me-1 text-primary"></i>
                            Matière <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('noteMatiereId') is-invalid @enderror"
                                wire:model="noteMatiereId"
                                @if(!$noteParcourId) disabled @endif>
                            @if(!$noteParcourId)
                                <option value="">-- Choisissez d'abord un parcours --</option>
                            @else
                                <option value="">-- Sélectionner une matière --</option>
                                @foreach($matieres ?? [] as $mat)
                                    <option value="{{ $mat->id }}">{{ $mat->nom }}</option>
                                @endforeach
                            @endif
                        </select>
                        @error('noteMatiereId') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <!-- Type de note -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-tag me-1 text-primary"></i>
                            Type de note <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('noteTypeId') is-invalid @enderror"
                                wire:model="noteTypeId">
                            <option value="">-- Sélectionner un type --</option>
                            @foreach($noteTypes ?? [] as $type)
                                <option value="{{ $type->id }}">{{ $type->nom }}</option>
                            @endforeach
                        </select>
                        @error('noteTypeId') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <!-- Note sur 20 -->
                    <div class="col-md-6">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-star me-1 text-primary"></i>
                            Note sur 20 <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="number"
                                   class="form-control @error('noteValeur') is-invalid @enderror"
                                   wire:model.lazy="noteValeur"
                                   min="0" max="20" step="0.01" placeholder="0.00">
                            <span class="input-group-text">/20</span>
                        </div>
                        @error('noteValeur') <div class="invalid-feedback">{{ $message }}</div> @enderror

                        <!-- Indicateur visuel de la note -->
                        @if($noteValeur && $noteValeur >= 0 && $noteValeur <= 20)
                        <div class="mt-2">
                            <div class="d-flex align-items-center">
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    <div class="progress-bar
                                        @if($noteValeur >= 16) bg-success
                                        @elseif($noteValeur >= 14) bg-info
                                        @elseif($noteValeur >= 10) bg-warning
                                        @else bg-danger
                                        @endif"
                                        role="progressbar"
                                        style="width: {{ ($noteValeur / 20) * 100 }}%"></div>
                                </div>
                                <small class="text-muted">
                                    @if($noteValeur >= 16) Excellent
                                    @elseif($noteValeur >= 14) Bien
                                    @elseif($noteValeur >= 10) Passable
                                    @else Insuffisant
                                    @endif
                                </small>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Observation -->
                    <div class="col-12">
                        <label class="form-label fw-semibold">
                            <i class="fa fa-comment me-1 text-primary"></i>
                            Observation
                        </label>
                        <textarea class="form-control @error('noteObservation') is-invalid @enderror"
                                  wire:model.lazy="noteObservation"
                                  rows="3" placeholder="Commentaire ou observation sur cette note..."></textarea>
                        @error('noteObservation') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        <div class="form-text">
                            <i class="fa fa-info-circle me-1"></i>
                            Ajoutez des détails sur cette évaluation (optionnel)
                        </div>
                    </div>
                </div>
          
        </div>
        <div class="modal-footer">
            <div class="d-flex justify-content-between w-100">
                <div>
                    <!-- Statut de sauvegarde -->
                    @if($isValidating)
                    <span class="text-muted">
                        <i class="fa fa-spinner fa-spin me-1"></i>Sauvegarde en cours...
                    </span>
                    @endif
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary" wire:click="quickCancel">
                        <i class="fa fa-times me-1"></i>Annuler
                    </button>
                    <button type="button" wire:click="saveNote" class="btn btn-primary" form="noteForm" wire:loading.attr="disabled">
                        <span wire:loading wire:target="saveNote" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        <i class="fa fa-save me-1" wire:loading.remove wire:target="saveNote"></i>
                        {{ $noteActionMode === 'edit' ? 'Mettre à jour' : 'Enregistrer' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<style>
/* Styles pour le modal Livewire */
.livewire-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    animation: fadeIn 0.3s ease-out;
}

.livewire-modal-content {
    background: white;
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    max-height: 90vh;
    overflow-y: auto;
    width: 100%;
    max-width: 800px;
    animation: slideInUp 0.3s ease-out;
}

.livewire-modal-content.modal-lg {
    max-width: 800px;
}

.modal-header {
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.form-label {
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-select, .form-control {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.input-group-text {
    border-radius: 0 0.5rem 0.5rem 0;
    border: 2px solid #e9ecef;
    border-left: none;
    background: #f8f9fa;
    font-weight: 600;
}

.progress {
    border-radius: 10px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: all 0.3s ease;
}

.alert {
    border-radius: 0.75rem;
    border: none;
}

.btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Styles pour les informations actuelles de la note */
.current-note-score {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    color: white;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.current-note-score.excellent {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.current-note-score.good {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.current-note-score.average {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.current-note-score.poor {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

.current-note-score::after {
    content: '/20';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: 500;
}

#currentNoteInfo {
    border-left: 4px solid #0dcaf0;
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 202, 240, 0.05) 100%);
    border-radius: 0.75rem;
    position: relative;
    overflow: hidden;
}

#currentNoteInfo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #0dcaf0, #0d6efd);
}

.text-sm {
    font-size: 0.875rem;
}

#currentObservationContainer .bg-light {
    background: rgba(248, 249, 250, 0.8) !important;
    border: 1px solid #e9ecef;
}

/* Animation pour l'apparition des infos actuelles */
#currentNoteInfo {
    animation: slideInDown 0.4s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Indicateur de changement */
.field-changed {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

.field-changed + .form-text {
    color: #ffc107;
    font-weight: 600;
}

.field-changed + .form-text::before {
    content: '⚠️ ';
}

/* Bouton de restauration */
.btn-outline-primary:hover {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* Animations */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Responsive */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-body, .modal-footer {
        padding: 1rem;
    }
}
</style>

<script>
// Raccourcis clavier pour le modal Livewire
document.addEventListener('keydown', function(e) {
    const modal = document.querySelector('.livewire-modal-overlay');
    if (modal) {
        // Escape pour fermer
        if (e.key === 'Escape') {
            e.preventDefault();
            @this.call('quickCancel');
        }

        // Ctrl+S pour sauvegarder
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            @this.call('saveNote');
        }
    }
});

// Gestion du focus automatique
document.addEventListener('livewire:load', function () {
    Livewire.hook('message.processed', (message, component) => {
        // Focus sur le premier champ quand le modal s'ouvre
        setTimeout(() => {
            const modal = document.querySelector('.livewire-modal-overlay');
            if (modal) {
                const firstInput = modal.querySelector('select, input, textarea');
                if (firstInput) {
                    firstInput.focus();
                }
            }
        }, 100);
    });
});
</script>
