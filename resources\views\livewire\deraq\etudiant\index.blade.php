@section('js')
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    <script src="{{ asset('js/plugins/select2/js/select2.full.min.js') }}"></script>
@endsection

@section('css')
    <link rel="stylesheet" href="{{ asset('js/plugins/select2/css/select2.min.css') }}">
    <style>
        .actions-menu .dropdown-item {
            padding: 0.25rem 1rem;
            font-size: 0.85rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.04);
            cursor: pointer;
        }

        .form-floating-sm .form-control {
            height: calc(1.5em + 0.5rem + 2px);
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .checkbox-select-all {
            margin-top: 0.25rem;
        }

        .table-filters {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .filter-item {
            flex: 1;
            min-width: 120px;
        }

        .btn-filter-toggle {
            min-width: 120px;
        }

        .badge-count {
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 0.7rem;
        }

        .sticky-actions {
            position: sticky;
            bottom: 0;
            background: white;
            padding: 0.75rem;
            border-top: 1px solid #eee;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            margin-top: 0.5rem;
        }

        .filter-tag {
            background: #f0f2f5;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
        }

        .filter-tag .remove {
            margin-left: 0.25rem;
            cursor: pointer;
            opacity: 0.6;
        }

        .filter-tag .remove:hover {
            opacity: 1;
        }

        /* Pour le mode compact */
        .table-sm td,
        .table-sm th {
            padding: 0.3rem;
        }

        .compact-view .d-sm-table-cell {
            display: none !important;
        }

        .compact-view td:first-child {
            padding-left: 0.75rem;
        }

        /* Améliorations visuelles */
        .avatar {
            width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .sortable:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .table-active {
            background-color: rgba(13, 110, 253, 0.1) !important;
        }

        .card {
            transition: all 0.2s ease;
        }

        .btn-group .btn {
            position: relative;
        }

        .badge-count {
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 0.65rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Animations de chargement */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .table-filters {
                flex-direction: column;
            }

            .filter-item {
                min-width: 100%;
                margin-bottom: 0.5rem;
            }

            /* Mobile-first hero section */
            .content-full {
                padding: 1rem;
            }

            .d-flex.gap-2 {
                flex-wrap: wrap;
                gap: 0.5rem !important;
            }

            /* Mobile table improvements */
            .table-responsive {
                border: none;
                box-shadow: none;
            }

            .table {
                font-size: 0.8rem;
            }

            .table td {
                padding: 0.5rem 0.25rem;
                border: none;
                border-bottom: 1px solid #eee;
            }

            /* Mobile actions */
            .sticky-actions {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 1rem;
                border-top: 2px solid #eee;
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }

            .sticky-actions .d-flex {
                flex-direction: column;
                gap: 0.5rem;
            }

            .sticky-actions .btn {
                width: 100%;
                justify-content: center;
            }

            /* Mobile modals */
            .modal-dialog {
                margin: 0;
                max-width: 100%;
                height: 100vh;
            }

            .modal-content {
                height: 100%;
                border-radius: 0;
            }

            .modal-body {
                padding: 1rem;
                overflow-y: auto;
            }

            /* Mobile cards */
            .card-body {
                padding: 1rem;
            }

            /* Mobile dropdowns */
            .dropdown-menu {
                position: fixed !important;
                top: auto !important;
                bottom: 60px !important;
                left: 1rem !important;
                right: 1rem !important;
                width: auto !important;
                max-height: 50vh;
                overflow-y: auto;
            }

            /* Touch-friendly buttons */
            .btn {
                min-height: 44px;
                padding: 0.75rem 1rem;
            }

            .btn-sm {
                min-height: 38px;
                padding: 0.5rem 0.75rem;
            }

            /* Mobile search */
            .input-group {
                margin-bottom: 1rem;
            }

            /* Hide less important columns on mobile */
            .d-none.d-sm-table-cell {
                display: none !important;
            }
        }

        @media (max-width: 576px) {
            /* Extra small devices */
            .content {
                padding: 0.5rem;
            }

            .block {
                margin-bottom: 1rem;
                border-radius: 0.5rem;
            }

            .block-content {
                padding: 1rem;
            }

            /* Stack hero elements */
            .d-flex.justify-content-between {
                flex-direction: column;
                align-items: stretch !important;
                gap: 1rem;
            }

            .d-flex.gap-2.align-items-center {
                justify-content: center;
            }

            /* Mobile table as cards */
            .table-responsive {
                display: block;
            }

            .table,
            .table thead,
            .table tbody,
            .table th,
            .table td,
            .table tr {
                display: block;
            }

            .table thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            .table tr {
                background: white;
                border: 1px solid #ddd;
                border-radius: 0.5rem;
                margin-bottom: 1rem;
                padding: 1rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .table td {
                border: none;
                position: relative;
                padding: 0.5rem 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .table td:before {
                content: attr(data-label);
                font-weight: bold;
                color: #666;
                flex: 0 0 40%;
            }

            /* Mobile pagination */
            .pagination {
                justify-content: center;
                flex-wrap: wrap;
            }

            .page-link {
                min-height: 44px;
                min-width: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .card {
                background-color: #2d3748;
                border-color: #4a5568;
            }

            .table {
                color: #e2e8f0;
            }

            .table-light {
                background-color: #4a5568;
                color: #e2e8f0;
            }

            .loading-overlay {
                background: rgba(45, 55, 72, 0.8);
                color: #e2e8f0;
            }
        }

        /* Print styles */
        @media print {
            .sticky-actions,
            .btn,
            .dropdown,
            .loading-overlay {
                display: none !important;
            }

            .table {
                font-size: 0.8rem;
            }

            .d-none.d-sm-table-cell {
                display: table-cell !important;
            }
        }
    </style>
@endsection

<div>
    <!-- Hero avec statistiques -->
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h1 class="h3 fw-bold mb-1">Gestion des Étudiants</h1>
                    <!-- Debug temporaire -->
                    {{-- <small class="text-muted">Debug: showAddInscriptionModal = {{ $showAddInscriptionModal ? 'true' : 'false' }}</small> --}}
                    <div class="d-flex gap-3 text-muted fs-sm">
                        <span><i class="fa fa-users me-1"></i> {{ $totalEtudiants }} étudiants</span>
                        @if($totalSupprimés > 0)
                            <span><i class="fa fa-trash me-1"></i> {{ $totalSupprimés }} supprimés</span>
                        @endif
                        @if($query || $filtreParcours || $filtreNiveau || $filtreAnnee)
                            <span class="badge bg-info">Filtres actifs</span>
                        @endif
                    </div>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <!-- Bouton de test temporaire -->
                    {{-- <button class="btn btn-sm btn-warning" wire:click="openAddInscriptionModal(1, 'Test User')" title="Test Modal">
                        <i class="fa fa-test me-1"></i> Test
                    </button> --}}

                    <!-- Boutons de vue -->
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-alt-secondary" wire:click="toggleCompactView"
                                title="{{ $compactView ? 'Vue normale' : 'Vue compacte' }}">
                            <i class="fa fa-{{ $compactView ? 'expand' : 'compress' }}"></i>
                        </button>
                        <button class="btn btn-alt-secondary" wire:click="toggleFilters"
                                title="{{ $showFilters ? 'Masquer filtres' : 'Afficher filtres' }}">
                            <i class="fa fa-filter"></i>
                        </button>
                    </div>

                    <button class="btn btn-sm btn-alt-secondary" wire:click="toggleSupprimesMode">
                        @if ($viewSupprimesMode)
                            <i class="fa fa-list me-1"></i> Liste principale
                        @else
                            <i class="fa fa-trash me-1"></i> Corbeille
                            @if($totalSupprimés > 0)
                                <span class="badge bg-danger badge-count">{{ $totalSupprimés }}</span>
                            @endif
                        @endif
                    </button>

                    <div class="dropdown d-inline-block">
                        <button class="btn btn-sm btn-primary dropdown-toggle" type="button" id="actionDropdown"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-plus me-1"></i> Actions
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown">
                            <li><button class="dropdown-item" wire:click="$toggle('showCreateModal')">
                                    <i class="fa fa-user-plus me-1"></i> Nouvel étudiant
                                </button></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><button class="dropdown-item" onclick="window.print()">
                                    <i class="fa fa-print me-1"></i> Imprimer la liste
                                </button></li>
                            <li><button class="dropdown-item" wire:click="exportStudents">
                                    <i class="fa fa-download me-1"></i> Exporter Excel
                                </button></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><button class="dropdown-item" wire:click="deleteSelected()"
                                    @if (count($selectedItems) == 0) disabled @endif>
                                    <i class="fa fa-trash me-1"></i> Supprimer sélectionnés
                                </button></li>
                            @if ($viewSupprimesMode)
                                <li><button class="dropdown-item" wire:click="restoreSelected()"
                                        @if (count($selectedItems) == 0) disabled @endif>
                                        <i class="fa fa-undo me-1"></i> Restaurer sélectionnés
                                    </button></li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <div class="block block-rounded">
            <div class="block-content block-content-full">
                <!-- Filtres et recherche améliorés -->
                @if($showFilters)
                <div class="card border-0 shadow-sm mb-3">
                    <div class="card-body p-3">
                        <div class="row g-3 align-items-center">
                            <!-- Recherche principale -->
                            <div class="col-md-4">
                                <label class="form-label fs-sm fw-medium mb-1">Recherche</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fa fa-search text-muted"></i>
                                    </span>
                                    <input type="search" wire:model.debounce.300ms="query"
                                           class="form-control border-start-0 ps-0"
                                           placeholder="Nom, prénom ou téléphone...">
                                    @if ($query)
                                        <button class="btn btn-outline-secondary" wire:click="$set('query', '')"
                                                title="Effacer la recherche">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>

                            <!-- Filtres -->
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Parcours</label>
                                <select wire:model="filtreParcours" class="form-select form-select-sm">
                                    <option value="">Tous</option>
                                    @foreach ($parcours as $parcour)
                                        <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Niveau</label>
                                <select wire:model="filtreNiveau" class="form-select form-select-sm">
                                    <option value="">Tous</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Année</label>
                                <select wire:model="filtreAnnee" class="form-select form-select-sm">
                                    <option value="">Toutes</option>
                                    @foreach ($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fs-sm fw-medium mb-1">Affichage</label>
                                <select wire:model="perPage" class="form-select form-select-sm">
                                    <option value="10">10 lignes</option>
                                    <option value="25">25 lignes</option>
                                    <option value="50">50 lignes</option>
                                    <option value="100">100 lignes</option>
                                </select>
                            </div>
                        </div>

                        <!-- Actions rapides -->
                        @if($query || $filtreParcours || $filtreNiveau || $filtreAnnee)
                        <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                            <div class="d-flex flex-wrap gap-1">
                                @if ($filtreParcours)
                                    <span class="badge bg-light text-dark">
                                        Parcours: {{ $parcours->firstWhere('id', $filtreParcours)?->sigle }}
                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('filtreParcours', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                @endif
                                @if ($filtreNiveau)
                                    <span class="badge bg-light text-dark">
                                        Niveau: {{ $niveaux->firstWhere('id', $filtreNiveau)?->nom }}
                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('filtreNiveau', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                @endif
                                @if ($filtreAnnee)
                                    <span class="badge bg-light text-dark">
                                        Année: {{ $annees->firstWhere('id', $filtreAnnee)?->nom }}
                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('filtreAnnee', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                @endif
                                @if ($query)
                                    <span class="badge bg-light text-dark">
                                        Recherche: "{{ $query }}"
                                        <button class="btn-close btn-close-sm ms-1" wire:click="$set('query', '')"
                                                aria-label="Supprimer ce filtre"></button>
                                    </span>
                                @endif
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" wire:click="clearAllFilters">
                                <i class="fa fa-times me-1"></i> Tout effacer
                            </button>
                        </div>
                        @endif
                    </div>
                </div>
                @endif



                <!-- Tableau amélioré -->
                <div class="table-responsive position-relative">
                    @if($isLoading)
                    <div class="loading-overlay">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <span class="text-muted">Chargement en cours...</span>
                    </div>
                    @endif

                    <table class="table table-hover table-vcenter {{ $compactView ? 'table-sm compact-view' : '' }} fs-sm">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 40px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               wire:model="selectAll"
                                               @if (count($selectedItems) == count($etudiants)) checked @endif
                                               title="Sélectionner tout">
                                    </div>
                                </th>
                                <th wire:click="sortBy('nom')" class="sortable" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        Étudiant
                                        @if($sortField === 'nom')
                                            <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                        @else
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        @endif
                                    </div>
                                </th>
                                <th class="d-none d-sm-table-cell" wire:click="sortBy('parcour_id')" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        Parcours
                                        @if($sortField === 'parcour_id')
                                            <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                        @else
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        @endif
                                    </div>
                                </th>
                                <th class="d-none d-sm-table-cell" wire:click="sortBy('niveau_id')" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        Niveau
                                        @if($sortField === 'niveau_id')
                                            <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                        @else
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        @endif
                                    </div>
                                </th>
                                <th class="d-none d-sm-table-cell" wire:click="sortBy('created_at')" style="cursor: pointer;">
                                    <div class="d-flex align-items-center">
                                        {{ $viewSupprimesMode ? 'Supprimé le' : 'Inscrit le' }}
                                        @if($sortField === 'created_at')
                                            <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                        @else
                                            <i class="fa fa-sort ms-1 text-muted"></i>
                                        @endif
                                    </div>
                                </th>
                                <th class="text-center" style="width: 120px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($etudiants as $etu)
                                <tr wire:key="row-{{ $etu->id }}"
                                    class="{{ in_array($etu->id, $selectedItems) ? 'table-active' : '' }}">
                                    <td data-label="Sélection">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                value="{{ $etu->id }}" wire:model="selectedItems">
                                        </div>
                                    </td>
                                    <td class="fw-semibold" data-label="Étudiant">
                                        <div class="d-flex align-items-center">
                                            @if(!$compactView)
                                            <div class="avatar avatar-sm me-2">
                                                <img src="{{ asset($etu->user->photo ?? 'media/avatars/avatar0.jpg') }}"
                                                     alt="Avatar" class="rounded-circle">
                                            </div>
                                            @endif
                                            <div>
                                                <div class="fw-semibold">
                                                    {{ $etu->user->nom }} {{ $etu->user->prenom }}
                                                    @if($etu->user->sexe === 'F')
                                                        <i class="fa fa-venus text-pink fs-xs ms-1" title="Femme"></i>
                                                    @else
                                                        <i class="fa fa-mars text-blue fs-xs ms-1" title="Homme"></i>
                                                    @endif
                                                </div>
                                                @if(!$compactView)
                                                <div class="fs-xs text-muted">
                                                    <i class="fa fa-phone me-1"></i>
                                                    {{ $etu->user->telephone1 ?? 'Aucun téléphone' }}
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-sm-table-cell" data-label="Parcours">
                                        @if ($etu->parcours == null)
                                            <span class="badge bg-warning text-dark">
                                                <i class="fa fa-exclamation-triangle me-1"></i>Non défini
                                            </span>
                                        @else
                                            <span class="badge bg-primary">{{ $etu->parcours->sigle }}</span>
                                            @if(!$compactView)
                                                <div class="fs-xs text-muted mt-1">{{ $etu->parcours->nom }}</div>
                                            @endif
                                        @endif
                                    </td>
                                    <td class="d-none d-sm-table-cell" data-label="Niveau">
                                        <span class="badge bg-info">{{ $etu->niveau->nom }}</span>
                                    </td>
                                    <td class="d-none d-sm-table-cell" data-label="{{ $viewSupprimesMode ? 'Supprimé le' : 'Inscrit le' }}">
                                        <div class="fs-sm">
                                            {{ $viewSupprimesMode ? $etu->deleted_at?->format('d/m/Y') : $etu->created_at->format('d/m/Y') }}
                                        </div>
                                        @if(!$compactView)
                                        <div class="fs-xs text-muted">
                                            {{ $viewSupprimesMode ? $etu->deleted_at?->diffForHumans() : $etu->created_at->diffForHumans() }}
                                        </div>
                                        @endif
                                    </td>
                                    <td class="text-center" data-label="Actions">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-alt-secondary dropdown-toggle"
                                                type="button" id="dropdownMenu{{ $etu->id }}"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end actions-menu"
                                                aria-labelledby="dropdownMenu{{ $etu->id }}">
                                                @if (!$viewSupprimesMode)
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="openEditModal({{ $etu->id }}, {{ $etu->user->id }})">
                                                            <i class="fa fa-pencil-alt me-1"></i> Modifier
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="openAddInscriptionModal({{ $etu->user->id }}, '{{ $etu->user->nom }} {{ $etu->user->prenom }}')">
                                                            <i class="fa fa-user-plus me-1 text-success"></i> Ajouter inscription
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="confirmDelete('{{ $etu->user->nom }} {{ $etu->user->prenom }}', {{ $etu->id }})">
                                                            <i class="fa fa-trash me-1"></i> Supprimer
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <hr class="dropdown-divider">
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item"
                                                            wire:click="showNotes({{ $etu->user->id }}, {{ $etu->parcours->id }}, {{ $etu->niveau->id }}, {{ $etu->annee->id }}, '{{ $etu->user->nom }} {{ $etu->user->prenom }}')">
                                                            <i class="fa fa-graduation-cap me-1"></i> Gérer les notes
                                                        </button>
                                                    </li>
                                                    @if ($etu->parcours)
                                                        <li>
                                                            <button class="dropdown-item"
                                                                wire:click="viewReleve({{ $etu->user->id }}, {{ $etu->parcours->id }}, {{ $etu->niveau->id }}, {{ $etu->annee->id }})">
                                                                <i class="fa fa-file-alt me-1"></i> Relevé
                                                            </button>
                                                        </li>
                                                    @endif
                                                @else
                                                    <li>
                                                        <button type="button" class="dropdown-item"
                                                            wire:click="restoreEtu({{ $etu->id }})">
                                                            <i class="fa fa-undo me-1"></i> Restaurer
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button type="button" class="dropdown-item text-danger"
                                                            wire:click="deleteDefEtu({{ $etu->id }})"
                                                            onclick="confirm('Suppression définitive. Continuer?') || event.stopImmediatePropagation()">
                                                            <i class="fa fa-times me-1"></i> Supprimer définitivement
                                                        </button>
                                                    </li>
                                                @endif
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fa fa-search fa-2x mb-2"></i>
                                            <p>Aucun étudiant trouvé</p>
                                            @if ($query || $filtreParcours || $filtreNiveau || $filtreAnnee)
                                                <button class="btn btn-sm btn-alt-secondary"
                                                    wire:click="$set('filtreParcours', ''); $set('filtreNiveau', ''); $set('filtreAnnee', ''); $set('query', '');">
                                                    Effacer les filtres
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        @if (count($etudiants) > 0)
                            <small class="text-muted">Affichage de {{ $etudiants->firstItem() }} à
                                {{ $etudiants->lastItem() }} sur {{ $etudiants->total() }} étudiants</small>
                        @endif
                    </div>
                    <div>
                        {{ $etudiants->links() }}
                    </div>
                </div>

                <!-- Actions groupées améliorées -->
                @if (count($selectedItems) > 0)
                    <div class="sticky-actions">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">{{ count($selectedItems) }} sélectionné(s)</span>
                            <small class="text-muted">Actions en lot disponibles</small>
                        </div>
                        <div class="d-flex gap-2 align-items-center">
                            @if (!$viewSupprimesMode)
                                <!-- Actions pour les étudiants actifs -->
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fa fa-edit me-1"></i> Modifier en lot
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><h6 class="dropdown-header">Changer le parcours</h6></li>
                                        @foreach($parcours as $parcour)
                                            <li>
                                                <button class="dropdown-item"
                                                        wire:click="bulkUpdateParcours({{ $parcour->id }})"
                                                        onclick="confirm('Changer le parcours de {{ count($selectedItems) }} étudiant(s) vers {{ $parcour->sigle }}?') || event.stopImmediatePropagation()">
                                                    {{ $parcour->sigle }} - {{ $parcour->nom }}
                                                </button>
                                            </li>
                                        @endforeach
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">Changer le niveau</h6></li>
                                        @foreach($niveaux as $niveau)
                                            <li>
                                                <button class="dropdown-item"
                                                        wire:click="bulkUpdateNiveau({{ $niveau->id }})"
                                                        onclick="confirm('Changer le niveau de {{ count($selectedItems) }} étudiant(s) vers {{ $niveau->nom }}?') || event.stopImmediatePropagation()">
                                                    {{ $niveau->nom }}
                                                </button>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>

                                <button type="button" class="btn btn-sm btn-outline-success"
                                        wire:click="exportSelected()"
                                        title="Exporter les étudiants sélectionnés">
                                    <i class="fa fa-download me-1"></i> Exporter
                                </button>

                                <button type="button" class="btn btn-sm btn-danger" wire:click="deleteSelected()"
                                        onclick="confirm('Supprimer les {{ count($selectedItems) }} éléments sélectionnés?') || event.stopImmediatePropagation()">
                                    <i class="fa fa-trash me-1"></i> Supprimer
                                </button>
                            @else
                                <!-- Actions pour les étudiants supprimés -->
                                <button type="button" class="btn btn-sm btn-success" wire:click="restoreSelected()">
                                    <i class="fa fa-undo me-1"></i> Restaurer la sélection
                                </button>

                                <button type="button" class="btn btn-sm btn-danger"
                                        wire:click="forceDeleteSelected()"
                                        onclick="confirm('ATTENTION: Supprimer définitivement les {{ count($selectedItems) }} éléments sélectionnés? Cette action est irréversible!') || event.stopImmediatePropagation()">
                                    <i class="fa fa-times me-1"></i> Supprimer définitivement
                                </button>
                            @endif

                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    wire:click="$set('selectedItems', [])" title="Annuler la sélection">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <!-- END Page Content -->

    <!-- Modals -->
    <!-- Modal Création Étudiant -->
    @if ($showCreateModal)
    <div class="modal fade show d-block"  tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createEtudiantModalLabel">Ajouter un nouvel étudiant</h5>
                    <button type="button" class="btn-close" wire:click="$toggle('showCreateModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="addUser">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="nom">Nom <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('newUser.nom') is-invalid @enderror"
                                    id="nom" wire:model.defer="newUser.nom" placeholder="Nom">
                                @error('newUser.nom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="prenom">Prénom <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control @error('newUser.prenom') is-invalid @enderror" id="prenom"
                                    wire:model.defer="newUser.prenom" placeholder="Prénom">
                                @error('newUser.prenom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="sexe">Sexe <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('newUser.sexe') is-invalid @enderror" id="sexe"
                                    wire:model.defer="newUser.sexe">
                                    <option value="">-- Sélectionner --</option>
                                    <option value="M">Homme</option>
                                    <option value="F">Femme</option>
                                </select>
                                @error('newUser.sexe')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="date_naissance">Date de naissance</label>
                                <input type="date"
                                    class="form-control @error('newUser.date_naissance') is-invalid @enderror"
                                    id="date_naissance" wire:model.defer="newUser.date_naissance">
                                @error('newUser.date_naissance')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="telephone1">Téléphone <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control @error('newUser.telephone1') is-invalid @enderror"
                                    id="telephone1" wire:model.defer="newUser.telephone1" placeholder="Téléphone">
                                @error('newUser.telephone1')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="telephone2">Téléphone 2</label>
                                <input type="text" class="form-control" id="telephone2"
                                    wire:model.defer="newUser.telephone2" placeholder="Téléphone 2">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="niveau_id">Niveau <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('newUser.niveau_id') is-invalid @enderror"
                                    id="niveau_id" wire:model.defer="newUser.niveau_id">
                                    <option value="">-- Sélectionner --</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                                @error('newUser.niveau_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="parcours_id">Parcours <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('newUser.parcour_id') is-invalid @enderror"
                                    id="parcours_id" wire:model.defer="newUser.parcour_id">
                                    <option value="">-- Sélectionner --</option>
                                    @foreach ($parcours as $parcour)
                                        <option value="{{ $parcour->id }}">{{ $parcour->sigle }} -
                                            {{ $parcour->libelle }}</option>
                                    @endforeach
                                </select>
                                @error('newUser.parcour_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label" for="adresse">Adresse</label>
                            <textarea class="form-control" id="adresse" wire:model.defer="newUser.adresse" rows="2"></textarea>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-alt-secondary"
                                wire:click="$toggle('showCreateModal')">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

    @if ($showEditModal)
    <!-- Modal Modification Étudiant -->
    <div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEtudiantModalLabel">Modifier l'étudiant</h5>
                    <button type="button" class="btn-close" wire:click="$toggle('showEditModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="updateUser">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_nom">Nom <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control @error('editUser.nom') is-invalid @enderror" id="edit_nom"
                                    wire:model.defer="editUser.nom">
                                @error('editUser.nom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_prenom">Prénom <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control @error('editUser.prenom') is-invalid @enderror"
                                    id="edit_prenom" wire:model.defer="editUser.prenom">
                                @error('editUser.prenom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_sexe">Sexe <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('editUser.sexe') is-invalid @enderror"
                                    id="edit_sexe" wire:model.defer="editUser.sexe">
                                    <option value="">-- Sélectionner --</option>
                                    <option value="M">Homme</option>
                                    <option value="F">Femme</option>
                                </select>
                                @error('editUser.sexe')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_telephone">Téléphone <span
                                        class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control @error('editUser.telephone1') is-invalid @enderror"
                                    id="edit_telephone" wire:model.defer="editUser.telephone1">
                                @error('editUser.telephone1')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label" for="edit_niveau_id">Niveau <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('editParcours.niveau_id') is-invalid @enderror"
                                    id="edit_niveau_id" wire:model.defer="editParcours.niveau_id">
                                    <option value="">-- Sélectionner --</option>
                                    @foreach ($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                                @error('editParcours.niveau_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label class="form-label" for="edit_parcour_id">Parcours <span
                                        class="text-danger">*</span></label>
                                <select class="form-select @error('editParcours.parcour_id') is-invalid @enderror"
                                    id="edit_parcour_id" wire:model.defer="editParcours.parcour_id">
                                    <option value="">-- Sélectionner --</option>
                                    @foreach ($parcours as $parcour)
                                        <option value="{{ $parcour->id }}">{{ $parcour->sigle }} -
                                            {{ $parcour->nom }}</option>
                                    @endforeach
                                </select>
                                @error('editParcours.parcour_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-alt-secondary"
                                wire:click="$toggle('showEditModal')">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

    @if($showDeleteModal)
    <!-- Modal Confirmation Suppression -->
    <div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">Confirmation de suppression</h5>
                    <button type="button" class="btn-close" wire:click="$toggle('showDeleteModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer l'étudiant <strong>{{ $etuName }}</strong> ?</p>
                    <p class="text-danger"><small>Cette action peut être annulée ultérieurement.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-alt-secondary" wire:click="$toggle('showDeleteModal')">Annuler</button>
                    <button type="button" class="btn btn-danger" wire:click="deleteUser">Supprimer</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

     <!-- Modal Ajout Inscription -->
    @if ($showAddInscriptionModal)
    <div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fa fa-user-plus me-2"></i>
                        Ajouter une inscription
                    </h5>
                    <button type="button" class="btn-close btn-close-white" wire:click="closeAddInscriptionModal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <!-- Informations de l'étudiant -->
                    <div class="alert alert-info mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="fa fa-user fa-2x"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="alert-heading mb-1">
                                    <i class="fa fa-info-circle me-1"></i>
                                    Étudiant sélectionné
                                </h6>
                                <div class="fw-bold">{{ $selectedUserName }}</div>
                                <small class="text-muted">
                                    <i class="fa fa-lightbulb me-1"></i>
                                    Ajoutez une nouvelle inscription pour cet étudiant
                                </small>
                            </div>
                        </div>
                    </div>

                    <form wire:submit.prevent="addInscription">
                        <div class="row g-3">
                            <!-- Année universitaire -->
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="fa fa-calendar me-1 text-success"></i>
                                    Année universitaire <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('newInscription.annee_universitaire_id') is-invalid @enderror"
                                        wire:model="newInscription.annee_universitaire_id">
                                    <option value="">-- Sélectionner une année --</option>
                                    @foreach($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                                @error('newInscription.annee_universitaire_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>

                            <!-- Parcours -->
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="fa fa-graduation-cap me-1 text-success"></i>
                                    Parcours <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('newInscription.parcour_id') is-invalid @enderror"
                                        wire:model="newInscription.parcour_id">
                                    <option value="">-- Sélectionner un parcours --</option>
                                    @foreach($parcours as $parc)
                                        <option value="{{ $parc->id }}">{{ $parc->sigle }} - {{ $parc->nom }}</option>
                                    @endforeach
                                </select>
                                @error('newInscription.parcour_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>

                            <!-- Niveau -->
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="fa fa-layer-group me-1 text-success"></i>
                                    Niveau <span class="text-danger">*</span>
                                </label>
                                <select class="form-select @error('newInscription.niveau_id') is-invalid @enderror"
                                        wire:model="newInscription.niveau_id">
                                    <option value="">-- Sélectionner un niveau --</option>
                                    @foreach($niveaux as $niveau)
                                        <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                                    @endforeach
                                </select>
                                @error('newInscription.niveau_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>

                            <!-- Informations supplémentaires -->
                            <div class="col-md-6">
                                <div class="card border-0 bg-light h-100">
                                    <div class="card-body">
                                        <h6 class="card-title text-success">
                                            <i class="fa fa-info-circle me-1"></i>
                                            Informations
                                        </h6>
                                        <ul class="list-unstyled mb-0 small">
                                            <li><i class="fa fa-check text-success me-1"></i> L'inscription sera créée immédiatement</li>
                                            <li><i class="fa fa-exclamation-triangle text-warning me-1"></i> Vérifiez que l'étudiant n'est pas déjà inscrit</li>
                                            <li><i class="fa fa-user-graduate text-info me-1"></i> L'étudiant pourra accéder aux cours</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Aperçu de l'inscription -->
                            @if(($newInscription['annee_universitaire_id'] ?? false) && ($newInscription['parcour_id'] ?? false) && ($newInscription['niveau_id'] ?? false))
                            <div class="col-12">
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">
                                        <i class="fa fa-eye me-1"></i>Aperçu de l'inscription
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Étudiant:</strong> {{ $selectedUserName }}<br>
                                            <strong>Année:</strong>
                                            @foreach($annees as $annee)
                                                @if($annee->id == $newInscription['annee_universitaire_id'])
                                                    {{ $annee->nom }}
                                                @endif
                                            @endforeach
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Parcours:</strong>
                                            @foreach($parcours as $parc)
                                                @if($parc->id == $newInscription['parcour_id'])
                                                    {{ $parc->sigle }} - {{ $parc->nom }}
                                                @endif
                                            @endforeach
                                            <br>
                                            <strong>Niveau:</strong>
                                            @foreach($niveaux as $niveau)
                                                @if($niveau->id == $newInscription['niveau_id'])
                                                    {{ $niveau->nom }}
                                                @endif
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" wire:click="closeAddInscriptionModal">
                        <i class="fa fa-times me-1"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-success" wire:loading.attr="disabled" wire:click="addInscription">
                        <span wire:loading wire:target="addInscription" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                        <i class="fa fa-plus me-1" wire:loading.remove wire:target="addInscription"></i>
                        Ajouter l'inscription
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
@include('components.toast-notifications')
</div>


<script>
    // Notifications améliorées
    

    window.addEventListener("showErrorMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-exclamation-triangle me-1',
            message: event.detail.message || 'Une erreur est survenue!',
            delay: 6000
        });
    });

    window.addEventListener("showInfoMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'info',
            icon: 'fa fa-info-circle me-1',
            message: event.detail.message,
            delay: 5000
        });
    });

    // Gestion des téléchargements
    window.addEventListener("downloadFile", event => {
        const link = document.createElement('a');
        link.href = event.detail.url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });

    // Date picker
    window.addEventListener("helperDatePicker", event => {
        One.helpersOnLoad(['jq-datepicker']);
    });

    // Amélioration de l'UX avec des raccourcis clavier
    document.addEventListener('keydown', function(e) {
        // Ctrl+N pour nouveau étudiant
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            @this.call('$toggle', 'showCreateModal');
        }

        // Escape pour fermer les modals
        if (e.key === 'Escape') {
            @this.call('$set', 'showCreateModal', false);
            @this.call('$set', 'showEditModal', false);
            @this.call('$set', 'showDeleteModal', false);
        }

        // Ctrl+F pour focus sur la recherche
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });

    // Auto-save des préférences utilisateur
    document.addEventListener('DOMContentLoaded', function() {
        // Restaurer les préférences
        const compactView = localStorage.getItem('student_compact_view') === 'true';
        const showFilters = localStorage.getItem('student_show_filters') !== 'false';

        if (compactView !== @this.compactView) {
            @this.set('compactView', compactView);
        }
        if (showFilters !== @this.showFilters) {
            @this.set('showFilters', showFilters);
        }
    });

    // Sauvegarder les préférences
    Livewire.on('compactViewChanged', (value) => {
        localStorage.setItem('student_compact_view', value);
    });

    Livewire.on('showFiltersChanged', (value) => {
        localStorage.setItem('student_show_filters', value);
    });

    // Amélioration de la sélection multiple
    document.addEventListener('click', function(e) {
        if (e.target.matches('input[type="checkbox"][wire\\:model="selectedItems"]')) {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][wire\\:model="selectedItems"]');
            const selectAllCheckbox = document.querySelector('input[wire\\:model="selectAll"]');

            if (selectAllCheckbox) {
                const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
                selectAllCheckbox.checked = checkedCount === checkboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
            }
        }
    });

    // Tooltip pour les actions
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>

   

<!-- Inclure les modals -->
@include('livewire.deraq.etudiant.modals.create-modal')
@include('livewire.deraq.etudiant.modals.edit-student-modal')